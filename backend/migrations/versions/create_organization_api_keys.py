"""Create organization API keys table

Revision ID: create_organization_api_keys
Revises: 52c38d0cdc3b
Create Date: 2023-11-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'create_organization_api_keys'
down_revision = '52c38d0cdc3b'  # Update this to your latest migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Enable the uuid-ossp extension if it's not already enabled
    op.execute('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"')
    
    # Check if organization_api_keys table already exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    if 'organization_api_keys' not in inspector.get_table_names():
        # Create organization_api_keys table
        op.create_table(
            'organization_api_keys',
            sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
            sa.Column('organization_id', postgresql.UUID(as_uuid=True), sa.<PERSON>ey('organization.id', ondelete='CASCADE'), nullable=False),
            sa.Column('openai_api_key', sa.String(), nullable=True),
            sa.Column('google_api_key', sa.String(), nullable=True),
            sa.Column('deepseek_api_key', sa.String(), nullable=True),
            sa.Column('deepseek_api_base', sa.String(), nullable=True),
            sa.UniqueConstraint('organization_id', name='uq_organization_api_keys_organization_id')
        )
        print("Table 'organization_api_keys' created successfully")
    else:
        print("Table 'organization_api_keys' already exists, skipping creation")


def downgrade() -> None:
    # Drop organization_api_keys table if it exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    if 'organization_api_keys' in inspector.get_table_names():
        op.drop_table('organization_api_keys')
        print("Table 'organization_api_keys' dropped")
    else:
        print("Table 'organization_api_keys' doesn't exist, skipping drop")
