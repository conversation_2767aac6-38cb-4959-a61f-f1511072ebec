import React, { useState, useEffect } from 'react';
import axios from 'axios';
import DocumentManager from './DocumentManager';

function ChatbotManager({ organization, user }) {
  const [chatbots, setChatbots] = useState([]);
  const [newChatbot, setNewChatbot] = useState({
    name: '',
    description: '',
    role: '',
    question: false,
    system_prompt: ''
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isOrgAdmin, setIsOrgAdmin] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [selectedChatbot, setSelectedChatbot] = useState(null);
  const [documentType, setDocumentType] = useState("context"); // Default to context
  const [uploadProgress, setUploadProgress] = useState(0);
  const [activeTab, setActiveTab] = useState("context"); // For tab navigation

  useEffect(() => {
    if (organization) {
      fetchChatbots();
      checkOrgAdminStatus();
    }
  }, [organization]);

  const checkOrgAdminStatus = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/organizations/${organization.id}/members`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Find current user in members list
      const currentUserMembership = response.data.find(
        member => member.user_id === user.id
      );
      
      setIsOrgAdmin(currentUserMembership?.role === 'admin');
    } catch (err) {
      console.error('Failed to check admin status:', err);
    }
  };

  const fetchChatbots = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/chatbots`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Filter chatbots by organization
      const orgChatbots = response.data.filter(bot => bot.organization_id === organization.id);
      setChatbots(orgChatbots);
      setLoading(false);
    } catch (err) {
      setError('Failed to fetch chatbots');
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewChatbot({
      ...newChatbot,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const createChatbot = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/chatbots`, {
        name: newChatbot.name,
        description: newChatbot.description,
        system_prompt: newChatbot.system_prompt,
        organization_id: organization.id
      }, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setChatbots([...chatbots, response.data]);
      setNewChatbot({
        name: '',
        description: '',
        role: '',
        question: false,
        system_prompt: ''
      });
    } catch (err) {
      setError('Failed to create chatbot');
    }
  };

  const fetchDocuments = async (type = activeTab) => {
    try {
      const token = localStorage.getItem('token');
      const params = { 
        org_id: organization.id,
        doc_type: type
      };
      if (selectedChatbot) params.chatbot_id = selectedChatbot;
      
      const response = await axios.get(`${API_URL}/api/documents`, {
        params,
        headers: { Authorization: `Bearer ${token}` }
      });
      
      setDocuments(response.data);
    } catch (err) {
      console.error('Failed to fetch documents:', err);
    }
  };

  // Update fetchDocuments call when tab changes
  useEffect(() => {
    fetchDocuments(activeTab);
  }, [activeTab, selectedChatbot]);

  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const handleChatbotSelect = (e) => {
    setSelectedChatbot(e.target.value);
  };

  const handleDocTypeChange = (e) => {
    setDocumentType(e.target.value);
  };

  const uploadDocument = async (e) => {
    e.preventDefault();
    if (!selectedFile) return;

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('org_id', organization.id);
    formData.append('doc_type', documentType); // Add document type
    if (selectedChatbot) formData.append('chatbot_id', selectedChatbot);

    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/api/ingest`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(percentCompleted);
        }
      });

      setSelectedFile(null);
      setUploadProgress(0);
      fetchDocuments(documentType);
      setActiveTab(documentType); // Switch to the tab of the uploaded document type
    } catch (err) {
      console.error('Failed to upload document:', err);
    }
  };

  const handleDeleteDocument = async (documentId) => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`${API_URL}/api/documents/${documentId}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        // Refresh the documents list
        fetchDocuments(selectedChatbot);
      } catch (err) {
        console.error('Failed to delete document:', err);
      }
    }
  };

  if (loading) {
    return <div>Loading chatbots...</div>;
  }

  const renderContextSection = () => (
    <div className="bg-gray-50 p-4 rounded-lg border mt-8">
      <h3 className="text-lg font-semibold mb-2">Knowledge Management</h3>
      
      {/* Tab navigation */}
      <div className="border-b mb-4">
        <nav className="flex -mb-px">
          <button
            onClick={() => setActiveTab("context")}
            className={`py-2 px-4 ${
              activeTab === "context"
                ? "border-b-2 border-blue-500 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Context Documents
          </button>
          <button
            onClick={() => setActiveTab("example")}
            className={`py-2 px-4 ${
              activeTab === "example"
                ? "border-b-2 border-blue-500 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Example Conversations
          </button>
        </nav>
      </div>
      
      <form onSubmit={uploadDocument} className="mb-6">
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Upload Document</label>
          <input
            type="file"
            onChange={handleFileChange}
            className="w-full px-3 py-2 border rounded"
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Document Type</label>
          <select
            value={documentType}
            onChange={handleDocTypeChange}
            className="w-full px-3 py-2 border rounded"
          >
            <option value="context">Context Document</option>
            <option value="example">Example Conversation</option>
          </select>
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Assign to Chatbot (Optional)</label>
          <select
            value={selectedChatbot || ''}
            onChange={handleChatbotSelect}
            className="w-full px-3 py-2 border rounded"
          >
            <option value="">All Chatbots</option>
            {chatbots.map(bot => (
              <option key={bot.id} value={bot.id}>{bot.name}</option>
            ))}
          </select>
        </div>
        
        {uploadProgress > 0 && (
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        )}
        
        <button
          type="submit"
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          disabled={!selectedFile}
        >
          Upload Document
        </button>
      </form>
      
      <h4 className="font-medium mb-2">
        {activeTab === "context" ? "Context Documents" : "Example Conversations"}
      </h4>
      {documents.length > 0 ? (
        <ul className="border rounded divide-y">
          {documents.map(doc => (
            <li key={doc.id} className="p-3 flex justify-between items-center">
              <div>
                <p className="font-medium">{doc.name}</p>
                <p className="text-sm text-gray-500">
                  {doc.chatbot_name ? `Assigned to: ${doc.chatbot_name}` : 'Available to all chatbots'}
                </p>
              </div>
              <button 
                onClick={() => handleDeleteDocument(doc.id)}
                className="text-red-500 hover:text-red-700"
              >
                Delete
              </button>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500">No {activeTab === "context" ? "context documents" : "example conversations"} uploaded yet.</p>
      )}
    </div>
  );

  return (
    <div className="mt-8">
      <h2 className="text-2xl font-bold mb-4">Chatbots</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {chatbots.map(bot => (
          <div key={bot.id} className="border rounded-lg p-4 shadow-sm">
            <h3 className="text-xl font-semibold">{bot.name}</h3>
            <p className="text-gray-600">{bot.description}</p>
            <div className="mt-4 flex justify-between">
              <span className={`px-2 py-1 rounded text-xs ${bot.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                {bot.is_active ? 'Active' : 'Inactive'}
              </span>
              <button 
                className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600"
                onClick={() => window.location.href = `/?chatbot=${bot.id}`}
              >
                Chat
              </button>
            </div>
          </div>
        ))}
      </div>
      
      {isOrgAdmin && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <h3 className="text-lg font-semibold mb-2">Create New Chatbot</h3>
          <form onSubmit={createChatbot}>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Name</label>
              <input
                type="text"
                value={newChatbot.name}
                onChange={handleInputChange}
                name="name"
                className="w-full px-3 py-2 border rounded"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Description</label>
              <textarea
                value={newChatbot.description}
                onChange={handleInputChange}
                name="description"
                className="w-full px-3 py-2 border rounded"
                rows="2"
              />
            </div>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Role</label>
              <input
                type="text"
                value={newChatbot.role}
                onChange={handleInputChange}
                name="role"
                className="w-full px-3 py-2 border rounded"
              />
            </div>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Question</label>
              <input
                type="checkbox"
                checked={newChatbot.question}
                onChange={handleInputChange}
                name="question"
                className="mr-2"
              />
              <span>Enable question mode</span>
            </div>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">System Prompt</label>
              <textarea
                value={newChatbot.system_prompt}
                onChange={handleInputChange}
                name="system_prompt"
                className="w-full px-3 py-2 border rounded"
                rows="4"
                placeholder="You are a helpful assistant..."
              />
            </div>
            <button
              type="submit"
              className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
            >
              Create Chatbot
            </button>
          </form>
        </div>
      )}
      {renderContextSection()}
      {/* Document Manager */}
      <div className="mt-8">
        <DocumentManager 
          organization={organization} 
          selectedChatbot={selectedChatbot} 
        />
      </div>
    </div>
  );
}

export default ChatbotManager;
