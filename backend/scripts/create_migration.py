import asyncio
import os
import sys
import argparse

# Add the parent directory to sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from alembic.config import Config
from alembic import command

def create_migration(message):
    # Get the absolute path to alembic.ini
    alembic_ini_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'alembic.ini'))
    
    # Create Alembic config
    alembic_cfg = Config(alembic_ini_path)
    
    # Create a new migration
    command.revision(alembic_cfg, autogenerate=True, message=message)
    print(f"Migration '{message}' created successfully!")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Create a new database migration')
    parser.add_argument('message', help='Migration message')
    args = parser.parse_args()
    
    create_migration(args.message)
