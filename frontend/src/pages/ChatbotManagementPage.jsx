import React, { useState, useEffect, useRef } from 'react';
import { useParams, Link } from 'react-router-dom';
import axios from 'axios';
import DocumentManager from '../components/DocumentManager';
import { useAuth } from '../hooks/useAuth.jsx';
import { API_URL } from '../api/index.js';

function ChatbotManagementPage() {
  const { orgId } = useParams();
  const { auth } = useAuth();
  const [chatbots, setChatbots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [newChatbot, setNewChatbot] = useState({
    name: '',
    description: '',
    role: '',
    question: false
  });
  const [isEditing, setIsEditing] = useState(false);
  const [editingId, setEditingId] = useState(null);
  const [organization, setOrganization] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [showForm, setShowForm] = useState(false);
  const documentManagerRef = useRef();
  const [selectedChatbot, setSelectedChatbot] = useState(null);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteStatus, setInviteStatus] = useState(null);

  useEffect(() => {
    if (orgId) {
      fetchChatbots();
      fetchOrganization();
    }
  }, [orgId]);

  const fetchOrganization = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/organizations/${orgId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      setOrganization(response.data);
      
      // Get user role in organization
      const membersResponse = await axios.get(`${API_URL}/api/organizations/${orgId}/members`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      if (auth && auth.user) {
        const currentUser = membersResponse.data.find(
          member => member.user_id === auth.user.id
        );
        setUserRole(currentUser?.role);
      } else {
        console.error('Auth user is not available');
        setError('User authentication information is missing');
      }
    } catch (err) {
      console.error('Error fetching organization:', err);
      setError('Failed to fetch organization details: ' + (err.response?.data?.detail || err.message));
    }
  };

  const fetchChatbots = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setLoading(false);
        return;
      }
      
      const response = await axios.get(`${API_URL}/api/chatbots`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // The backend already filters chatbots by user access
      const orgChatbots = response.data.filter(bot => bot.organization_id === orgId);
      setChatbots(orgChatbots);
      
      // If we don't have the user role yet, fetch it
      if (userRole === null && auth && auth.user) {
        try {
          const membersResponse = await axios.get(`${API_URL}/api/organizations/${orgId}/members`, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          
          const currentUser = membersResponse.data.find(
            member => member.user_id === auth.user.id
          );
          
          const role = currentUser?.role;
          setUserRole(role);
          
          // Only redirect non-admin users with a single chatbot
          if (orgChatbots.length === 1 && role !== 'admin') {
            window.location.href = `/org/${orgId}/chat/${orgChatbots[0].id}`;
          }
        } catch (roleErr) {
          console.error('Error fetching user role:', roleErr);
        }
      } else if (orgChatbots.length === 1 && userRole !== 'admin') {
        // We already have the role and can make the redirect decision
        window.location.href = `/org/${orgId}/chat/${orgChatbots[0].id}`;
      }
      
      setLoading(false);
    } catch (err) {
      console.error('Error fetching chatbots:', err);
      setError('Failed to fetch chatbots. ' + (err.response?.data?.detail || err.message));
      setLoading(false);
    }
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setNewChatbot({
      ...newChatbot,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const token = localStorage.getItem('token');
    
    try {
      let response;
      
      if (isEditing) {
        // Update existing chatbot
        response = await axios.put(`${API_URL}/api/chatbots/${editingId}`, 
          newChatbot,
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
      } else {
        // Create new chatbot
        response = await axios.post(`${API_URL}/api/chatbots`, 
          {
            ...newChatbot,
            organization_id: orgId
          },
          {
            headers: {
              Authorization: `Bearer ${token}`
            }
          }
        );
        
        // Associate any pending documents with the new chatbot
        if (documentManagerRef.current && documentManagerRef.current.pendingDocuments.length > 0) {
          await documentManagerRef.current.associateDocumentsWithChatbot(response.data.id);
        }
      }
      
      // Reset the document manager
      if (documentManagerRef.current && documentManagerRef.current.reset) {
        documentManagerRef.current.reset();
      }
      
      // Reset form and refresh chatbots
      setNewChatbot({
        name: '',
        description: '',
        role: '',
        question: false
      });
      setIsEditing(false);
      setEditingId(null);
      setShowForm(false); // Hide the form after successful submission
      fetchChatbots();
    } catch (err) {
      setError(isEditing ? 'Failed to update chatbot' : 'Failed to create chatbot');
    }
  };

  const handleCancelCreate = async () => {
    // Clean up any pending documents if creation is canceled
    if (documentManagerRef.current) {
      await documentManagerRef.current.cleanupPendingDocuments();
    }
    
    setNewChatbot({
      name: '',
      description: '',
      role: '',
      question: false
    });
    setIsEditing(false);
    setEditingId(null);
    setShowForm(false); // Hide the form when canceling
  };

  const handleEdit = (chatbot) => {
    setNewChatbot({
      name: chatbot.name,
      description: chatbot.description || '',
      role: chatbot.role || '',
      question: chatbot.question || false
    });
    setIsEditing(true);
    setEditingId(chatbot.id);
    setShowForm(true); // Show the form when editing
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this chatbot?')) {
      try {
        const token = localStorage.getItem('token');
        await axios.delete(`${API_URL}/api/chatbots/${id}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        fetchChatbots();
      } catch (err) {
        setError('Failed to delete chatbot');
      }
    }
  };

  const handleInvite = async (e) => {
    e.preventDefault();
    setInviteStatus(null);
    
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/invitations`, 
        {
          email: inviteEmail,
          organization_id: orgId,
          chatbot_id: selectedChatbot?.id
        },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      
      // Use the invitation ID as the token
      const invitationToken = response.data.id;
      const invitationLink = `${window.location.origin}/join-organization?token=${invitationToken}`;
      
      setInviteStatus({ 
        type: 'success', 
        message: `Invitation created for ${inviteEmail}`,
        link: invitationLink
      });
    } catch (error) {
      setInviteStatus({ 
        type: 'error', 
        message: error.response?.data?.detail || 'Failed to send invitation' 
      });
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-full">Loading...</div>;
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{organization?.name} - Chatbots</h1>
        
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      {/* Existing Chatbots Section - Now at the top */}
      <div className="bg-white shadow-md rounded p-6 mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Your Chatbots</h2>
          {!showForm && (
            <button
              onClick={() => {
                setIsEditing(false);
                setEditingId(null);
                setNewChatbot({
                  name: '',
                  description: '',
                  role: '',
                  question: false
                });
                setShowForm(true);
              }}
              className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            >
              Create New Chatbot
            </button>
          )}
        </div>
        
        {chatbots.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No chatbots created yet.</p>
            {!showForm && (
              <button
                onClick={() => setShowForm(true)}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Create Your First Chatbot
              </button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {chatbots.map(chatbot => (
              <div key={chatbot.id} className="border rounded p-4">
                <h3 className="font-bold text-lg">{chatbot.name}</h3>
                {chatbot.description && <p className="text-gray-600 mb-2">{chatbot.description}</p>}
                {chatbot.role && <p className="text-gray-600 mb-2"><strong>Role:</strong> {chatbot.role}</p>}
                <p className="text-gray-600 mb-2">
                  <strong>Type:</strong> {chatbot.question ? 'Question-Answering' : 'Conversational'}
                </p>
                <div className="flex mt-4 justify-between">
                  <div>
                    <button
                      onClick={() => handleEdit(chatbot)}
                      className="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDelete(chatbot.id)}
                      className="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded mr-2"
                    >
                      Delete
                    </button>
                    <button
                      onClick={() => {
                        setSelectedChatbot(chatbot);
                        setShowInviteModal(true);
                      }}
                      className="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded"
                    >
                      Invite
                    </button>
                  </div>
                  <button
                    onClick={() => window.location.href = `/org/${orgId}/chat/${chatbot.id}`}
                    className="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded"
                  >
                    Launch Chatbot
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Create/Edit Form - Only shown when showForm is true */}
      {showForm && (
        <div className="bg-white shadow-md rounded p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">{isEditing ? 'Edit Chatbot' : 'Create New Chatbot'}</h2>
          
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Name
              </label>
              <input
                type="text"
                name="name"
                value={newChatbot.name}
                onChange={handleInputChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                required
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Description
              </label>
              <input
                type="text"
                name="description"
                value={newChatbot.description}
                onChange={handleInputChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-bold mb-2">
                Role
              </label>
              <input
                type="text"
                name="role"
                value={newChatbot.role}
                onChange={handleInputChange}
                className="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="e.g., Life Coach, Career Advisor, etc."
              />
            </div>
            
            <div className="mb-4">
              <label className="flex items-center text-gray-700 text-sm font-bold">
                <input
                  type="checkbox"
                  name="question"
                  checked={newChatbot.question}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                Is Question-Answering Bot
              </label>
            </div>
            
            {/* Document Manager - shown for both new and existing chatbots */}
            {organization && (
              <div className="mb-6 border-t pt-4 mt-4">
                <h3 className="text-lg font-semibold mb-2">Manage Documents</h3>
                <p className="mb-4 text-gray-600">
                  Upload and manage documents for this chatbot. These documents will be used to provide context and examples for the chatbot's responses.
                </p>
                <DocumentManager 
                  organization={organization} 
                  selectedChatbot={editingId} 
                  ref={documentManagerRef}
                />
              </div>
            )}
            
            <div className="flex items-center justify-between mt-6">
              <button
                type="submit"
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                {isEditing ? 'Update Chatbot' : 'Create Chatbot'}
              </button>
              
              <button
                type="button"
                onClick={handleCancelCreate}
                className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">
              Invite User to {selectedChatbot ? `"${selectedChatbot.name}"` : organization?.name}
            </h2>
            <p className="mb-4 text-gray-600">
              {selectedChatbot 
                ? `They will have access to the "${selectedChatbot.name}" chatbot.`
                : 'They will have access to all resources in this organization.'}
            </p>
            
            <form onSubmit={handleInvite}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Email Address</label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              {inviteStatus && (
                <div className={`p-3 mb-4 rounded ${
                  inviteStatus.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  <p>{inviteStatus.message}</p>
                  {inviteStatus.link && (
                    <div className="mt-2">
                      <p className="font-semibold">Share this link with the user:</p>
                      <div className="flex mt-1">
                        <input 
                          type="text" 
                          value={inviteStatus.link} 
                          className="flex-grow p-2 border rounded-l text-sm bg-white" 
                          readOnly 
                        />
                        <button
                          onClick={() => {
                            navigator.clipboard.writeText(inviteStatus.link);
                            alert('Link copied to clipboard!');
                          }}
                          className="bg-blue-500 text-white px-3 rounded-r"
                        >
                          Copy
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => {
                    setShowInviteModal(false);
                    setSelectedChatbot(null);
                  }}
                  className="mr-2 px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Send Invitation
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default ChatbotManagementPage;
