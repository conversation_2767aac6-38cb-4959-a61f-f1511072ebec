import os
import openai
from google import genai

openai.api_key = os.getenv("OPENAI_API_KEY")
gemini_model = os.getenv("GEMINI_EMBEDDING_MODEL", "text-embedding-004")

# OpenAI provider
def openai_embedding(text: str) -> list[float]:
    response = openai.Embedding.create(
        input=[text],
        model="text-embedding-ada-002"
    )
    return response['data'][0]['embedding']

def gemini_embedding(text: str) -> list[float]:
    client = genai.Client(api_key=os.getenv("GOOGLE_API_KEY"))
    response = client.models.embed_content(
        model=gemini_model,
        contents=text)
    # Extract the actual embedding values from the ContentEmbedding object
    return response.embeddings[0].values

# Add more providers here in future
EMBEDDING_PROVIDERS = {
    "openai": openai_embedding,
    "gemini": gemini_embedding,
    # "huggingface": huggingface_embedding,
    # "cohere": cohere_embedding,
}

def get_embedding_provider(name: str):
    return EMBEDDING_PROVIDERS.get(name.lower())

def get_embedding_providers():
    return list(EMBEDDING_PROVIDERS.keys())

def get_embedding_dimension(provider: str) -> int:
    """Return the dimension of embeddings for the given provider"""
    if provider.lower() == "openai":
        return 1536  # OpenAI ada-002 dimension
    elif provider.lower() == "gemini":
        return 768  # Gemini text-embedding-004 dimension
    else:
        return 384  # Default dimension
