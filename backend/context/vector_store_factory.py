import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get vector store type from environment
VECTOR_STORE_TYPE = os.getenv("VECTOR_STORE_TYPE", "weaviate").lower()

def get_vector_store():
    """
    Factory function to get the appropriate vector store module
    based on environment configuration
    """
    if VECTOR_STORE_TYPE == "pinecone":
        logger.info("Using Pinecone vector store")
        from context import pinecone_store
        return pinecone_store
    else:
        logger.info("Using Weaviate vector store")
        from context import vector_store
        return vector_store

def store_chunks(chunks, document_id=None, document_name=None, doc_type="context", org_id=None, chatbot_id=None):
    """Store chunks using the configured vector store"""
    vector_store = get_vector_store()
    return vector_store.store_chunks(
        chunks=chunks,
        document_id=document_id,
        document_name=document_name,
        doc_type=doc_type,
        org_id=org_id,
        chatbot_id=chatbot_id
    )

def search_chunks(query, chatbot_id=None, org_id=None, doc_type=None, limit=3):
    """Search chunks using the configured vector store"""
    vector_store = get_vector_store()
    return vector_store.search_chunks(
        query=query,
        chatbot_id=chatbot_id,
        org_id=org_id,
        doc_type=doc_type,
        limit=limit
    )