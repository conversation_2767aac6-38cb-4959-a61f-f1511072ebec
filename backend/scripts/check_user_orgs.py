import asyncio
import os
import sys
from uuid import UUID

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from sqlalchemy import select, join
from sqlalchemy.ext.asyncio import AsyncSession
from database import engine
from models.user import User
from models.organization import Organization
from models.user_organization import user_organization

async def check_user_orgs(user_email=None, user_id=None):
    async with AsyncSession(engine) as session:
        if user_email:
            # Find user by email
            result = await session.execute(
                select(User).where(User.email == user_email)
            )
            user = result.scalars().first()
        elif user_id:
            # Find user by ID
            try:
                user_uuid = UUID(user_id)
                result = await session.execute(
                    select(User).where(User.id == user_uuid)
                )
                user = result.scalars().first()
            except ValueError:
                print(f"Invalid UUID format: {user_id}")
                return
        else:
            # List all users
            result = await session.execute(select(User))
            users = result.scalars().all()
            print(f"Found {len(users)} users:")
            for u in users:
                print(f"  - {u.id}: {u.email}")
            return
        
        if not user:
            print(f"User not found")
            return
        
        print(f"User: {user.id} ({user.email})")
        
        # Find organizations for this user
        result = await session.execute(
            select(Organization)
            .join(user_organization)
            .where(user_organization.c.user_id == user.id)
        )
        orgs = result.scalars().all()
        
        print(f"Found {len(orgs)} organizations for user {user.email}:")
        for org in orgs:
            print(f"  - {org.id}: {org.name}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Check if argument looks like an email
        arg = sys.argv[1]
        if '@' in arg:
            asyncio.run(check_user_orgs(user_email=arg))
        else:
            asyncio.run(check_user_orgs(user_id=arg))
    else:
        asyncio.run(check_user_orgs())