import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth.jsx';
import axios from 'axios';

function JoinOrganizationPage() {
  const [searchParams] = useSearchParams();
  const inviteToken = searchParams.get('token');
  const [orgName, setOrgName] = useState('');
  const [inviterName, setInviterName] = useState('');
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isValidatingToken, setIsValidatingToken] = useState(true);
  
  const { register, joinOrganization, auth } = useAuth();

  // Validate the invitation token
  useEffect(() => {
    if (!inviteToken) {
      setIsValidatingToken(false);
      return;
    }

    axios.get(`${API_URL}/api/invitations/validate?token=${inviteToken}`)
      .then(response => {
        setOrgName(response.data.organization_name);
        setInviterName(response.data.inviter_name);
        setEmail(response.data.email);
        setIsValidatingToken(false);
      })
      .catch(error => {
        setError('Invalid or expired invitation link');
        setIsValidatingToken(false);
      });
  }, [inviteToken]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      setIsLoading(false);
      return;
    }

    try {
      console.log("Submitting join organization form");
      console.log("Auth state:", auth.isAuthenticated);
      
      if (auth.isAuthenticated) {
        console.log("User is already authenticated, joining organization directly");
        // If already logged in, just join the organization
        await joinOrganization(inviteToken);
      } else {
        console.log("User is not authenticated, registering first");
        // Register first
        await register(name, email, password);
        console.log("Registration successful, auth state:", auth);
        
        // We need to manually call joinOrganization after a delay
        // to ensure the auth state is updated
        setTimeout(async () => {
          try {
            console.log("Attempting to join organization after registration");
            console.log("Current auth state:", auth);
            
            // Manually get the token since auth state might not be updated yet
            const token = localStorage.getItem('token');
            console.log("Token from localStorage:", token);
            
            if (token) {
              // Make the API call directly instead of using the hook function
              await axios.post(`${API_URL}/api/invitations/accept`,
                { token: inviteToken },
                {
                  headers: {
                    Authorization: `Bearer ${token}`
                  }
                }
              );
              
              console.log("Successfully joined organization");
              
              // Get the user's organizations
              const orgResponse = await axios.get(`${API_URL}/api/organizations`, {
                headers: {
                  Authorization: `Bearer ${token}`
                }
              });
              
              console.log("Organizations after joining:", orgResponse.data);
              
              if (orgResponse.data && orgResponse.data.length > 0) {
                // Navigate to the first organization's chatbots page
                window.location.href = `/org/${orgResponse.data[0].id}/chatbots`;
              } else {
                setError("Failed to join organization. No organizations found after joining.");
                setIsLoading(false);
              }
            } else {
              setError("Authentication failed. Please try logging in directly.");
              setIsLoading(false);
            }
          } catch (joinError) {
            console.error("Error joining organization after registration:", joinError);
            setError(joinError.response?.data?.detail || 'Failed to join organization after registration.');
            setIsLoading(false);
          }
        }, 1500); // Increased delay to ensure auth state is updated
      }
    } catch (err) {
      console.error("Error in join organization flow:", err);
      setError(err.response?.data?.detail || 'Failed to join organization. Please try again.');
      setIsLoading(false);
    }
  };

  if (isValidatingToken) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <p className="text-lg">Validating invitation...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Join {orgName}
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            You've been invited by {inviterName} to join their organization
          </p>
        </div>
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="rounded-md shadow-sm -space-y-px">
            <div>
              <label htmlFor="name" className="sr-only">Full Name</label>
              <input
                id="name"
                name="name"
                type="text"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Full Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="email-address" className="sr-only">Email address</label>
              <input
                id="email-address"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                readOnly={!!email} // Make email read-only if it came from the invitation
              />
            </div>
            <div>
              <label htmlFor="password" className="sr-only">Password</label>
              <input
                id="password"
                name="password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>
            <div>
              <label htmlFor="confirm-password" className="sr-only">Confirm Password</label>
              <input
                id="confirm-password"
                name="confirm-password"
                type="password"
                autoComplete="new-password"
                required
                className="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Confirm Password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            </div>
          </div>

          <div>
            <button
              type="submit"
              disabled={isLoading}
              className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {isLoading ? 'Creating account...' : 'Join Organization'}
            </button>
          </div>
          
          <div className="text-sm text-center">
            <Link to="/login" className="font-medium text-blue-600 hover:text-blue-500">
              Already have an account? Sign in
            </Link>
          </div>
          <div className="text-sm text-center">
            <Link to="/register" className="font-medium text-blue-600 hover:text-blue-500">
              Want to create your own organization? Sign up
            </Link>
          </div>
        </form>
      </div>
    </div>
  );
}

export default JoinOrganizationPage;
