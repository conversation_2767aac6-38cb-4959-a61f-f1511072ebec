"""Update chatbot columns

Revision ID: update_chatbot_columns
Revises: create_invitation_table
Create Date: 2023-10-18 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'update_chatbot_columns'
down_revision = 'create_invitation_table'  # This should be the last successful migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Use raw SQL to add columns if they don't exist
    op.execute("""
    DO $$
    BEGIN
        -- Add role column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'role'
        ) THEN
            ALTER TABLE chatbot ADD COLUMN role VARCHAR;
            RAISE NOTICE 'Added role column to chatbot table';
        END IF;

        -- Add question column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'question'
        ) THEN
            ALTER TABLE chatbot ADD COLUMN question BOOLEAN DEFAULT FALSE;
            RAISE NOTICE 'Added question column to chatbot table';
        END IF;

        -- Add settings column if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'settings'
        ) THEN
            ALTER TABLE chatbot ADD COLUMN settings JSONB DEFAULT '{}';
            RAISE NOTICE 'Added settings column to chatbot table';
        END IF;

        -- Remove system_prompt column if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'system_prompt'
        ) THEN
            ALTER TABLE chatbot DROP COLUMN system_prompt;
            RAISE NOTICE 'Removed system_prompt column from chatbot table';
        END IF;
    END $$;
    """)


def downgrade() -> None:
    # Use raw SQL to revert changes
    op.execute("""
    DO $$
    BEGIN
        -- Add system_prompt column back if it doesn't exist
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'system_prompt'
        ) THEN
            ALTER TABLE chatbot ADD COLUMN system_prompt VARCHAR;
            RAISE NOTICE 'Added system_prompt column back to chatbot table';
        END IF;

        -- Remove settings column if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'settings'
        ) THEN
            ALTER TABLE chatbot DROP COLUMN settings;
            RAISE NOTICE 'Removed settings column from chatbot table';
        END IF;

        -- Remove question column if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'question'
        ) THEN
            ALTER TABLE chatbot DROP COLUMN question;
            RAISE NOTICE 'Removed question column from chatbot table';
        END IF;

        -- Remove role column if it exists
        IF EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'role'
        ) THEN
            ALTER TABLE chatbot DROP COLUMN role;
            RAISE NOTICE 'Removed role column from chatbot table';
        END IF;
    END $$;
    """)