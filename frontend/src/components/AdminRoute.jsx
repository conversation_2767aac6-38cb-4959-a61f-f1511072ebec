import React, { useState, useEffect } from 'react';
import { Navigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth';
import { API_URL } from '../api/index.js';

function AdminRoute({ children }) {
  const { orgId } = useParams();
  const { auth } = useAuth();
  const [isAdmin, setIsAdmin] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAdminStatus = async () => {
      try {
        const token = localStorage.getItem('token');
        const response = await axios.get(`${API_URL}/api/organizations/${orgId}/members`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        
        const currentUser = response.data.find(
          member => member.user_id === auth.user.id
        );
        
        setIsAdmin(currentUser?.role === 'admin');
        setLoading(false);
      } catch (err) {
        console.error('Failed to check admin status:', err);
        setIsAdmin(false);
        setLoading(false);
      }
    };
    
    checkAdminStatus();
  }, [orgId, auth.user]);

  if (loading) {
    return <div className="flex justify-center items-center h-full">Loading...</div>;
  }

  return isAdmin ? children : <Navigate to={`/org/${orgId}/chatbots`} replace />;
}

export default AdminRoute;
