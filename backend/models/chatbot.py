from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, <PERSON><PERSON>ey, JSO<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime
from database import Base  # Fix the import path if needed

class Chatbot(Base):
    __tablename__ = 'chatbot'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    description = Column(String)
    role = Column(String)  # Field for role
    question = Column(Boolean, default=False)  # Field for question checkbox
    settings = Column(JSON, default={})
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    organization_id = Column(UUID(as_uuid=True), ForeignKey('organization.id'))
    created_by = Column(UUID(as_uuid=True), Foreign<PERSON><PERSON>('user.id'))
    
    # Relationships
    organization = relationship("Organization", back_populates="chatbots")
    creator = relationship("User")
    # We'll add these relationships later
    # contexts = relationship("Context", back_populates="chatbot")
    # examples = relationship("Example", back_populates="chatbot")
