import asyncio
import os
import sys

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from sqlalchemy import select, insert
from sqlalchemy.ext.asyncio import AsyncSession
from database import engine
from models.user import User
from models.organization import Organization
from models.user_organization import user_organization

async def fix_user_org_relationships():
    async with AsyncSession(engine) as session:
        # Get all users
        result = await session.execute(select(User))
        users = result.scalars().all()
        
        # Get all organizations
        result = await session.execute(select(Organization))
        orgs = result.scalars().all()
        
        print(f"Found {len(users)} users and {len(orgs)} organizations")
        
        for user in users:
            # Check if user has any organizations
            result = await session.execute(
                select(Organization)
                .join(user_organization)
                .where(user_organization.c.user_id == user.id)
            )
            user_orgs = result.scalars().all()
            
            print(f"User {user.email} has {len(user_orgs)} organizations")
            
            # If user has no organizations but organizations exist, assign the first one
            if len(user_orgs) == 0 and len(orgs) > 0:
                print(f"Adding user {user.email} to organization {orgs[0].name}")
                
                # Add user to the first organization
                await session.execute(
                    insert(user_organization).values(
                        user_id=user.id,
                        organization_id=orgs[0].id
                    )
                )
                await session.commit()
                print(f"User {user.email} added to organization {orgs[0].name}")

if __name__ == "__main__":
    asyncio.run(fix_user_org_relationships())