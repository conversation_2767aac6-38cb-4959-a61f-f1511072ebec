import logging
import uuid
import os
from context.embeddings import get_embedding

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get the embedding provider from environment
embedding_provider = os.getenv("EMBEDDING_PROVIDER", "gemini")

def store_chunks(chunks, document_id=None, document_name=None, doc_type="context", org_id=None, chatbot_id=None):
    """Store text chunks in Pinecone"""
    from ai.pinecone_client import index
    
    for i, chunk in enumerate(chunks):
        try:
            # Generate embedding using the configured provider
            embedding = get_embedding(chunk, provider=embedding_provider)
            
            # Create metadata
            metadata = {
                "text": chunk,
                "document_id": document_id,
                "document_name": document_name,
                "org_id": org_id,
                "chatbot_id": chatbot_id,
                "doc_type": doc_type
            }
            
            # Filter out None values
            metadata = {k: v for k, v in metadata.items() if v is not None}
            
            # Generate a unique ID for this vector
            vector_id = f"{document_id}_{i}" if document_id else str(uuid.uuid4())
            
            # Upsert to Pinecone
            index.upsert(
                vectors=[(vector_id, embedding, metadata)]
            )
            
        except Exception as e:
            logger.error(f"Error storing chunk in Pinecone: {e}")

def search_chunks(query, chatbot_id=None, org_id=None, doc_type=None, limit=3):
    """
    Search for chunks in Pinecone based on semantic similarity
    
    Args:
        query (str): The search query text
        chatbot_id (str, optional): Filter by chatbot ID
        org_id (str, optional): Filter by organization ID
        doc_type (str, optional): Filter by document type
        limit (int, optional): Maximum number of results to return
        
    Returns:
        list: List of matching chunks with their metadata
    """
    logger.info(f"Searching chunks with query: '{query}'")
    logger.info(f"Search parameters - chatbot_id: {chatbot_id}, org_id: {org_id}, doc_type: {doc_type}, limit: {limit}")
    
    try:
        from ai.pinecone_client import index
        from context.embeddings import get_embedding
        
        # Get embedding for query using the configured provider
        query_embedding = get_embedding(query, provider=embedding_provider)
        
        # Build filter
        filter_dict = {}
        if chatbot_id:
            filter_dict["chatbot_id"] = chatbot_id
        if org_id:
            filter_dict["org_id"] = org_id
        if doc_type:
            filter_dict["doc_type"] = doc_type
        
        # Query Pinecone
        results = index.query(
            vector=query_embedding,
            filter=filter_dict if filter_dict else None,
            top_k=limit,
            include_metadata=True
        )
        
        # Process results
        chunks = []
        for match in results.matches:
            metadata = match.metadata
            chunk = {
                "text": metadata.get("text", ""),
                "document_id": metadata.get("document_id", ""),
                "document_name": metadata.get("document_name", ""),
                "chatbot_id": metadata.get("chatbot_id", ""),
                "org_id": metadata.get("org_id", ""),
                "doc_type": metadata.get("doc_type", "context")
            }
            chunks.append(chunk)
        
        logger.info(f"Found {len(chunks)} matching chunks")
        if chunks:
            logger.info(f"First chunk text: {chunks[0]['text'][:100]}...")
        
        return chunks
        
    except Exception as e:
        logger.error(f"Error searching chunks in Pinecone: {e}")
        return []
