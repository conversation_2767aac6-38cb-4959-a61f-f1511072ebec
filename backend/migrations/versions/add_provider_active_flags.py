"""Add provider active flags

Revision ID: add_provider_active_flags
Revises: create_organization_api_keys
Create Date: 2023-11-02 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_provider_active_flags'
down_revision = 'create_organization_api_keys'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if organization_api_keys table exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    if 'organization_api_keys' in inspector.get_table_names():
        # Add active flag columns if they don't exist
        columns = [c['name'] for c in inspector.get_columns('organization_api_keys')]
        
        if 'openai_active' not in columns:
            op.add_column('organization_api_keys', sa.Column('openai_active', sa.<PERSON>(), server_default='false'))
        
        if 'gemini_active' not in columns:
            op.add_column('organization_api_keys', sa.Column('gemini_active', sa.<PERSON>(), server_default='false'))
        
        if 'deepseek_active' not in columns:
            op.add_column('organization_api_keys', sa.Column('deepseek_active', sa.<PERSON>an(), server_default='false'))
        
        if 'test_active' not in columns:
            op.add_column('organization_api_keys', sa.Column('test_active', sa.Boolean(), server_default='true'))
        
        print("Added active flag columns to organization_api_keys table")
    else:
        print("Table 'organization_api_keys' doesn't exist, skipping column addition")


def downgrade() -> None:
    # Check if organization_api_keys table exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    if 'organization_api_keys' in inspector.get_table_names():
        # Remove active flag columns if they exist
        columns = [c['name'] for c in inspector.get_columns('organization_api_keys')]
        
        if 'openai_active' in columns:
            op.drop_column('organization_api_keys', 'openai_active')
        
        if 'gemini_active' in columns:
            op.drop_column('organization_api_keys', 'gemini_active')
        
        if 'deepseek_active' in columns:
            op.drop_column('organization_api_keys', 'deepseek_active')
        
        if 'test_active' in columns:
            op.drop_column('organization_api_keys', 'test_active')
        
        print("Removed active flag columns from organization_api_keys table")
    else:
        print("Table 'organization_api_keys' doesn't exist, skipping column removal")