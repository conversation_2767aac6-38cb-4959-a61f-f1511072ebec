import os
import sys

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from alembic.config import Config
from alembic import command
from alembic.script import ScriptDirectory

def check_migration_heads():
    try:
        # Get the absolute path to alembic.ini
        alembic_ini_path = os.path.abspath(os.path.join(current_dir, '..', 'alembic.ini'))
        
        print(f"Using alembic.ini at: {alembic_ini_path}")
        
        # Create Alembic config
        alembic_cfg = Config(alembic_ini_path)
        
        # Get script directory
        script = ScriptDirectory.from_config(alembic_cfg)
        
        # Get all heads
        heads = script.get_heads()
        
        print(f"\nFound {len(heads)} migration heads:")
        for head in heads:
            print(f"- {head}")
        
        if len(heads) > 1:
            print("\nTo merge these heads, run:")
            heads_str = " ".join(heads)
            print(f"docker compose exec backend alembic merge -m 'merge heads' {heads_str}")
        
        # Show current migration state
        print("\nCurrent migration state:")
        command.current(alembic_cfg, verbose=True)
        
    except Exception as e:
        print(f"Error checking migration heads: {e}")

if __name__ == "__main__":
    print(f"Current directory: {os.getcwd()}")
    print(f"Script directory: {current_dir}")
    print(f"Parent directory: {parent_dir}")
    
    check_migration_heads()
