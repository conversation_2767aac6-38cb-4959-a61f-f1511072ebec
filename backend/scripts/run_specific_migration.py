import os
import sys

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from alembic.config import Config
from alembic import command

def run_specific_migration(target_revision):
    try:
        # Get the absolute path to alembic.ini
        alembic_ini_path = os.path.abspath(os.path.join(current_dir, '..', 'alembic.ini'))
        
        print(f"Using alembic.ini at: {alembic_ini_path}")
        print(f"Target revision: {target_revision}")
        
        # Create Alembic config
        alembic_cfg = Config(alembic_ini_path)
        
        # Run the specific migration
        command.upgrade(alembic_cfg, target_revision)
        print(f"Migration to {target_revision} applied successfully!")
    except Exception as e:
        print(f"Error running migration: {e}")

if __name__ == "__main__":
    print(f"Current directory: {os.getcwd()}")
    print(f"Script directory: {current_dir}")
    print(f"Parent directory: {parent_dir}")
    
    if len(sys.argv) < 2:
        print("Usage: python run_specific_migration.py <target_revision>")
        print("Example: python run_specific_migration.py update_chatbot_columns")
        sys.exit(1)
    
    target_revision = sys.argv[1]
    run_specific_migration(target_revision)