"""Add role to user_organization

Revision ID: add_role_to_user_organization
Revises: 
Create Date: 2023-10-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_role_to_user_organization'
down_revision = None  # Update this to your previous migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add role column to user_organization table if it doesn't exist
    op.execute("""
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name='user_organization' AND column_name='role'
        ) THEN
            ALTER TABLE user_organization ADD COLUMN role VARCHAR DEFAULT 'member';
        END IF;
    END
    $$;
    """)
    
    # Set all existing records to 'admin' for now
    # We can update this later with more specific logic if needed
    op.execute("""
    UPDATE user_organization
    SET role = 'admin';
    """)


def downgrade() -> None:
    # Remove the role column if needed
    op.drop_column('user_organization', 'role')
