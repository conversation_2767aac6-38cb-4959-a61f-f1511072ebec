// Root route component that handles initial navigation
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth.jsx';

export default function Index() {
  const { auth } = useAuth();
  const navigate = useNavigate();
  
  useEffect(() => {
    const checkOrganizations = async () => {
      if (auth.isAuthenticated) {
        try {
          // Fetch user's organizations
          const response = await fetch('/api/organizations', {
            headers: {
              'Authorization': `Bearer ${auth.token}`
            }
          });
          
          if (!response.ok) {
            throw new Error('Failed to fetch organizations');
          }
          
          const organizations = await response.json();
          
          // If user has organizations, redirect to the first one
          // Otherwise, redirect to create organization page
          if (organizations && organizations.length > 0) {
            navigate(`/organizations/${organizations[0].id}`);
          } else {
            navigate('/organizations/new');
          }
        } catch (error) {
          console.error('Error checking organizations:', error);
          // Default to organization creation on error
          navigate('/organizations/new');
        }
      } else {
        // Not authenticated, redirect to login
        navigate('/login');
      }
    };
    
    checkOrganizations();
  }, [auth.isAuthenticated, auth.token, navigate]);
  
  return <div>Loading...</div>;
}
