import asyncio
import os
import sys

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from sqlalchemy import text
from database import engine

async def update_chatbot_schema():
    try:
        async with engine.begin() as conn:
            print("Checking and updating chatbot table schema...")
            
            # Add role column if it doesn't exist
            await conn.execute(text("""
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'chatbot' AND column_name = 'role'
                    ) THEN
                        ALTER TABLE chatbot ADD COLUMN role VARCHAR;
                        RAISE NOTICE 'Added role column to chatbot table';
                    ELSE
                        RAISE NOTICE 'Role column already exists in chatbot table';
                    END IF;
                END $$;
            """))
            
            # Add question column if it doesn't exist
            await conn.execute(text("""
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'chatbot' AND column_name = 'question'
                    ) THEN
                        ALTER TABLE chatbot ADD COLUMN question BOOLEAN DEFAULT FALSE;
                        RAISE NOTICE 'Added question column to chatbot table';
                    ELSE
                        RAISE NOTICE 'Question column already exists in chatbot table';
                    END IF;
                END $$;
            """))
            
            # Add settings column if it doesn't exist
            await conn.execute(text("""
                DO $$
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.columns 
                        WHERE table_name = 'chatbot' AND column_name = 'settings'
                    ) THEN
                        ALTER TABLE chatbot ADD COLUMN settings JSONB DEFAULT '{}';
                        RAISE NOTICE 'Added settings column to chatbot table';
                    ELSE
                        RAISE NOTICE 'Settings column already exists in chatbot table';
                    END IF;
                END $$;
            """))
            
            print("Chatbot table schema updated successfully!")
    except Exception as e:
        print(f"Error updating chatbot schema: {e}")

if __name__ == "__main__":
    asyncio.run(update_chatbot_schema())