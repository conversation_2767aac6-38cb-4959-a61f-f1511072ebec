import logging
import os
import pinecone
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize Pinecone
pinecone_api_key = os.getenv("PINECONE_API_KEY")
pinecone_environment = os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp")
index_name = os.getenv("PINECONE_INDEX", "mydocs")
embedding_provider = os.getenv("EMBEDDING_PROVIDER", "gemini")

# Initialize Pinecone client
pinecone.init(api_key=pinecone_api_key, environment=pinecone_environment)

# Check if index exists, if not create it
if index_name not in pinecone.list_indexes():
    from context.embeddings_providers import get_embedding_dimension
    dimension = get_embedding_dimension(embedding_provider)
    
    pinecone.create_index(
        name=index_name,
        dimension=dimension,  # Use the dimension from the embedding provider
        metric="cosine"
    )
    logger.info(f"Created new index '{index_name}' with dimension {dimension}")

# Get index
index = pinecone.Index(index_name)

def get_relevant_context(query_text, chatbot_id=None, doc_type="context", limit=3):
    """
    Get relevant context for a query from Pinecone
    
    Args:
        query_text (str): The query text
        chatbot_id (str, optional): Filter by chatbot ID
        doc_type (str, optional): Filter by document type
        limit (int, optional): Maximum number of results
        
    Returns:
        str: Concatenated text of relevant chunks
    """
    logger.info(f"Getting relevant context for query: '{query_text}'")
    logger.info(f"Parameters - chatbot_id: {chatbot_id}, doc_type: {doc_type}, limit: {limit}")
    
    try:
        from context.embeddings import get_embedding
        
        # Get embedding for query using the configured provider
        query_embedding = get_embedding(query_text, provider=embedding_provider)
        
        # Build filter
        filter_dict = {}
        if chatbot_id:
            filter_dict["chatbot_id"] = chatbot_id
        if doc_type:
            filter_dict["doc_type"] = doc_type
        
        # Query Pinecone
        results = index.query(
            vector=query_embedding,
            filter=filter_dict if filter_dict else None,
            top_k=limit,
            include_metadata=True
        )
        
        # Process results
        docs = []
        for match in results.matches:
            metadata = match.metadata
            doc_text = metadata.get("text", "")
            doc_name = metadata.get("document_name", "Unknown document")
            docs.append(f"From {doc_name}:\n{doc_text}")
        
        logger.info(f"Found {len(docs)} relevant documents")
        return "\n\n".join(docs)
        
    except Exception as e:
        logger.error(f"Pinecone error: {e}")
        return ""
