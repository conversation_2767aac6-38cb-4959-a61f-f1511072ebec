from sqlalchemy import Column, String, DateTime, func, ForeignKey, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from datetime import datetime
import uuid
from pydantic import BaseModel, ConfigDict
from typing import Optional, List
from database import Base

# Association table for many-to-many relationship between users and organizations
user_organization = Table(
    "user_organization",
    Base.metadata,
    Column("user_id", UUID(as_uuid=True), ForeignKey("user.id", ondelete="CASCADE"), primary_key=True),
    Column("organization_id", UUID(as_uuid=True), ForeignKey("organization.id", ondelete="CASCADE"), primary_key=True),
    Column("role", String, default="member")
)

class Organization(Base):
    __tablename__ = "organization"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    users = relationship("User", secondary=user_organization, back_populates="organizations")
    chatbots = relationship("Chatbot", back_populates="organization", cascade="all, delete-orphan")
    api_keys = relationship("OrganizationApiKeys", back_populates="organization", cascade="all, delete-orphan", uselist=False)

# Pydantic models for API
class OrganizationBase(BaseModel):
    name: str
    description: Optional[str] = None

class OrganizationCreate(OrganizationBase):
    pass

class OrganizationRead(OrganizationBase):
    id: uuid.UUID
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class OrganizationUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
