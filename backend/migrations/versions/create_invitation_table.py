"""Create invitation table

Revision ID: create_invitation_table
Revises: add_role_to_user_organization
Create Date: 2023-10-16 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'create_invitation_table'
down_revision = 'add_role_to_user_organization'  # Update this to your previous migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Check if invitation table already exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    if 'invitation' not in inspector.get_table_names():
        # Create invitation table
        op.create_table(
            'invitation',
            sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
            sa.Column('email', sa.String(), nullable=False),
            sa.Column('organization_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('organization.id'), nullable=False),
            sa.Column('created_by', postgresql.UUID(as_uuid=True), sa.<PERSON>ey('user.id'), nullable=False),
            sa.Column('created_at', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
            sa.Column('expires_at', sa.DateTime(), nullable=False),
            sa.Column('used_at', sa.DateTime(), nullable=True),
            sa.Column('token', sa.String(), nullable=False, unique=True),
        )
    else:
        print("Table 'invitation' already exists, skipping creation")


def downgrade() -> None:
    # Drop invitation table if it exists
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    if 'invitation' in inspector.get_table_names():
        op.drop_table('invitation')
    else:
        print("Table 'invitation' doesn't exist, skipping drop")
