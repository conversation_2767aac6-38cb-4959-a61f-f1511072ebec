from sqlalchemy import Column, String, Foreign<PERSON><PERSON>, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
from pydantic import BaseModel, ConfigDict
from typing import Optional
from database import Base

class OrganizationApiKeys(Base):
    __tablename__ = "organization_api_keys"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organization.id", ondelete="CASCADE"), nullable=False)
    
    # API Keys
    openai_api_key = Column(String, nullable=True)
    google_api_key = Column(String, nullable=True)
    deepseek_api_key = Column(String, nullable=True)
    deepseek_api_base = Column(String, nullable=True)
    
    # Active flags
    openai_active = Column(Boolean, default=False)
    gemini_active = Column(Boolean, default=False)
    deepseek_active = Column(Boolean, default=False)
    test_active = Column(Boolean, default=True)  # Test provider is active by default
    
    # Relationship
    organization = relationship("Organization", back_populates="api_keys")

# Pydantic models for API
class ApiKeysBase(BaseModel):
    openai_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    deepseek_api_key: Optional[str] = None
    deepseek_api_base: Optional[str] = None
    
    # Active flags
    openai_active: Optional[bool] = False
    gemini_active: Optional[bool] = False
    deepseek_active: Optional[bool] = False
    test_active: Optional[bool] = True

class ApiKeysCreate(ApiKeysBase):
    organization_id: uuid.UUID

class ApiKeysRead(ApiKeysBase):
    id: uuid.UUID
    organization_id: uuid.UUID
    
    model_config = ConfigDict(from_attributes=True)

class ApiKeysUpdate(ApiKeysBase):
    pass
