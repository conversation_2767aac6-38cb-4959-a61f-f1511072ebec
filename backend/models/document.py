import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, ForeignKey, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from database import Base

class Document(Base):
    __tablename__ = 'document'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    doc_type = Column(String, nullable=False)  # 'context' or 'example'
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Foreign keys
    org_id = Column(UUID(as_uuid=True), ForeignKey('organization.id'), nullable=False)
    chatbot_id = Column(UUID(as_uuid=True), ForeignKey('chatbot.id'), nullable=True)
    
    # Relationships
    organization = relationship("Organization", back_populates="documents")
    chatbot = relationship("Chatbot", back_populates="documents")

# Update Organization model to include documents relationship
# This should be done after the Document class is defined
def setup_relationships():
    from models.organization import Organization
    from models.chatbot import Chatbot
    
    # Only set up relationships if they don't already exist
    if not hasattr(Organization, 'documents'):
        Organization.documents = relationship("Document", back_populates="organization")
    
    if not hasattr(Chatbot, 'documents'):
        Chatbot.documents = relationship("Document", back_populates="chatbot")

# Call the function to set up relationships
setup_relationships()
