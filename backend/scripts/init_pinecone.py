import os
import sys
import time
from dotenv import load_dotenv
import logging
from pinecone import Pinecone, ServerlessSpec

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Pinecone credentials
pinecone_api_key = os.getenv("PINECONE_API_KEY")
pinecone_cloud = os.getenv("PINECONE_CLOUD", "aws")
pinecone_region = os.getenv("PINECONE_REGION", "us-east-1")
index_name = os.getenv("PINECONE_INDEX", "mydocs")
embedding_provider = os.getenv("EMBEDDING_PROVIDER", "gemini")

def create_index():
    try:
        # Import here to avoid circular imports
        from context.embeddings_providers import get_embedding_dimension
        
        # Get the embedding dimension for the configured provider
        dimension = get_embedding_dimension(embedding_provider)
        logger.info(f"Using embedding provider: {embedding_provider} with dimension: {dimension}")
        
        # Initialize Pinecone
        pc = Pinecone(api_key=pinecone_api_key)
        logger.info(f"Connected to Pinecone")

        # Check if index exists
        existing_indexes = pc.list_indexes().names()
        if index_name in existing_indexes:
            # Delete existing index
            pc.delete_index(index_name)
            logger.info(f"Deleted existing index '{index_name}'")

        # Create new index with ServerlessSpec
        pc.create_index(
            name=index_name,
            dimension=dimension,  # Use the dimension from the embedding provider
            metric="cosine",
            spec=ServerlessSpec(
                cloud=pinecone_cloud,
                region=pinecone_region
            )
        )
        logger.info(f"Created new index '{index_name}' with dimension {dimension}")

        # Get index
        index = pc.Index(index_name)
        
        # Add a test document
        from context.embeddings import get_embedding
        
        test_text = "This is a test document to verify Pinecone is working correctly."
        test_embedding = get_embedding(test_text, provider=embedding_provider)
        
        index.upsert(
            vectors=[
                ("test-doc-001", test_embedding, {
                    "text": test_text,
                    "document_id": "test-doc-001",
                    "document_name": "test_document.txt",
                    "org_id": "test-org",
                    "chatbot_id": "test-chatbot",
                    "doc_type": "context"
                })
            ]
        )
        logger.info("Test document added to index")

        # Wait a moment for indexing to complete
        logger.info("Waiting for indexing to complete...")
        time.sleep(5)

        # Check index stats
        stats = index.describe_index_stats()
        logger.info(f"Index stats: {stats}")

        # Verify the document was added
        results = index.query(
            vector=test_embedding,
            top_k=1,
            include_metadata=True
        )

        if results.matches:
            logger.info(f"Verification: Found {len(results.matches)} documents in index")
            logger.info(f"First match score: {results.matches[0].score}")
        else:
            logger.warning("Verification: No documents found in index")
            logger.info("This might be due to indexing delay - the document may still be processing")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing Pinecone: {e}")
        return False

if __name__ == "__main__":
    success = create_index()
    if success:
        logger.info("Pinecone initialization completed successfully")
    else:
        logger.error("Pinecone initialization failed")
