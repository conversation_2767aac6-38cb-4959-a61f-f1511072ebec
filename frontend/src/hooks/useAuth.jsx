import { useState, useEffect, createContext, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../api/index.js';

// Create a context for authentication
const AuthContext = createContext(null);

// Provider component that wraps your app and makes auth available to any child component
export function AuthProvider({ children }) {
  const [auth, setAuth] = useState({
    isAuthenticated: false,
    token: null,
    user: null,
    loading: true
  });
  const navigate = useNavigate();

  // Check if user is already logged in on mount
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (!token) {
      setAuth(prev => ({ ...prev, loading: false }));
      return;
    }

    console.log("Token found in localStorage, checking validity");
    
    // Fetch user data to validate token
    fetchUserData(token)
      .then(userData => {
        console.log("User data fetched successfully:", userData);
        
        setAuth({
          isAuthenticated: true,
          token,
          user: userData,
          loading: false
        });
      })
      .catch(error => {
        console.error("Error fetching user data:", error);
        // If token is invalid, clear it
        localStorage.removeItem('token');
        setAuth({
          isAuthenticated: false,
          token: null,
          user: null,
          loading: false
        });
      });
  }, []);

  // Function to fetch user data
  const fetchUserData = async (token) => {
    const response = await axios.get(`${API_URL}/api/users/me`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  };

  // Function to check organizations and navigate
  const checkAndNavigate = async (token) => {
    try {
      console.log("Checking user organizations...");
      // Fetch user's organizations
      const response = await axios.get(`${API_URL}/api/organizations`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      if (!response.ok) {
        console.error("Failed to fetch organizations:", response.status);
        throw new Error('Failed to fetch organizations');
      }
      
      const organizations = await response.json();
      console.log("Organizations:", organizations);
      
      // If user has organizations, redirect to the first one
      // Otherwise, redirect to create organization page
      if (organizations && organizations.length > 0) {
        console.log("Navigating to organization:", organizations[0].id);
        navigate(`/org/${organizations[0].id}/chatbots`);
      } else {
        console.log("No organizations found, navigating to create org page");
        navigate('/setup-organization');
      }
    } catch (error) {
      console.error('Error checking organizations:', error);
      // Default to organization creation on error
      navigate('/setup-organization');
    }
  };

  // Login function
  const login = async (email, password) => {
    try {
      console.log("Attempting login for:", email);
      
      // Login with FastAPI-Users
      const response = await axios.post(`${API_URL}/api/auth/jwt/login`, 
        new URLSearchParams({
          username: email,
          password: password
        }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      });
      
      console.log("Login successful, token received");
      const token = response.data.access_token;
      
      // Store token
      localStorage.setItem('token', token);
      
      // Get user data
      const userData = await fetchUserData(token);
      
      // Update auth state
      setAuth({
        isAuthenticated: true,
        token,
        user: userData,
        loading: false
      });
      
      // Check organizations and navigate
      try {
        console.log("Fetching organizations");
        const orgResponse = await axios.get(`${API_URL}/api/organizations`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        console.log("Organizations fetched:", orgResponse.data);
        
        if (orgResponse.data && orgResponse.data.length > 0) {
          console.log("User has organizations, navigating to first org");
          navigate(`/org/${orgResponse.data[0].id}/chatbots`);
        } else {
          console.log("No organizations found, navigating to setup");
          navigate('/setup-organization');
        }
      } catch (orgError) {
        console.error("Error fetching organizations:", orgError);
        navigate('/setup-organization');
      }
      
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  // Register function
  const register = async (name, email, password) => {
    try {
      // Register with FastAPI-Users
      await axios.post(`${API_URL}/api/auth/register`, {
        email,
        password,
        name
      });
      
      // Login after registration
      return await login(email, password);
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('token');
    setAuth({
      isAuthenticated: false,
      token: null,
      user: null,
      loading: false
    });
    navigate('/login');
  };

  // Create organization function
  const createOrganization = async (name, description) => {
    try {
      const response = await axios.post(`${API_URL}/api/organizations`, 
        { name, description },
        {
          headers: {
            Authorization: `Bearer ${auth.token}`
          }
        }
      );
      
      // Navigate to the new organization
      navigate(`/org/${response.data.id}/chatbots`);
      
      return response.data;
    } catch (error) {
      console.error('Error creating organization:', error);
      throw error;
    }
  };

  // Join organization function
  const joinOrganization = async (inviteToken) => {
    try {
      console.log("Joining organization with token:", inviteToken);
      console.log("Current auth token:", auth.token);
      
      // Make sure we have a valid auth token
      if (!auth.token) {
        console.error("No auth token available");
        throw new Error("You must be logged in to join an organization");
      }
      
      const response = await axios.post(`${API_URL}/api/invitations/accept`,
        { token: inviteToken },
        {
          headers: {
            Authorization: `Bearer ${auth.token}`
          }
        }
      );
      
      console.log("Successfully joined organization:", response.data);
      
      // Refresh organizations and navigate
      const orgResponse = await axios.get(`${API_URL}/api/organizations`, {
        headers: {
          Authorization: `Bearer ${auth.token}`
        }
      });
      
      console.log("Organizations after joining:", orgResponse.data);
      
      if (orgResponse.data && orgResponse.data.length > 0) {
        // Navigate to the first organization's chatbots page
        navigate(`/org/${orgResponse.data[0].id}/chatbots`);
      } else {
        console.error("No organizations found after joining");
        navigate('/setup-organization');
      }
      
      return true;
    } catch (error) {
      console.error('Error joining organization:', error.response?.data || error.message);
      throw error;
    }
  };

  // Return the provider with all auth functions
  return (
    <AuthContext.Provider value={{ 
      auth, 
      login, 
      logout, 
      register, 
      createOrganization,
      joinOrganization
    }}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook for components to get the auth object and re-render when it changes
export const useAuth = () => {
  return useContext(AuthContext);
};
