import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../api';

function DocumentManager({ organization, selectedChatbot, isPendingChatbot = false, onDocumentsUploaded = () => {} }, ref) {
  const [documents, setDocuments] = useState([]);
  const [pendingDocuments, setPendingDocuments] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [documentType, setDocumentType] = useState("context");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [activeTab, setActiveTab] = useState("context");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!isPendingChatbot) {
      fetchDocuments(activeTab);
    }
  }, [activeTab, selectedChatbot, isPendingChatbot]);

  const fetchDocuments = async (type = activeTab) => {
    setIsLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const params = { 
        org_id: organization.id,
        doc_type: type
      };
      
      // For existing chatbots, filter by chatbot_id
      if (selectedChatbot && !isPendingChatbot) {
        params.chatbot_id = selectedChatbot;
        console.log(`Fetching documents for existing chatbot: ${selectedChatbot}`);
      } else {
        // For new chatbot creation, only show unassigned documents
        params.unassigned_only = true;
        console.log('Fetching unassigned documents for new chatbot');
      }
      
      console.log('Document fetch params:', params);
      
      const response = await axios.get(`${API_URL}/api/documents`, {
        params,
        headers: { Authorization: `Bearer ${token}` }
      });
      
      console.log(`Fetched ${response.data.length} documents`);
      setDocuments(response.data);
    } catch (err) {
      console.error('Failed to fetch documents:', err);
      setError('Failed to load documents. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e) => {
    setSelectedFile(e.target.files[0]);
  };

  const handleDocTypeChange = (e) => {
    setDocumentType(e.target.value);
  };

  const uploadDocument = async (e) => {
    e.preventDefault();
    if (!selectedFile) return;

    // Add logging to debug document type
    console.log('Uploading document with type:', documentType);
    console.log('Selected chatbot:', selectedChatbot);
    console.log('Is pending chatbot:', isPendingChatbot);

    const formData = new FormData();
    formData.append('file', selectedFile);
    formData.append('org_id', organization.id);
    formData.append('doc_type', documentType);
    
    // Only append chatbot_id if we have a real chatbot (not pending)
    if (selectedChatbot && !isPendingChatbot) {
      formData.append('chatbot_id', selectedChatbot);
    }

    try {
      setError(null);
      const token = localStorage.getItem('token');
      const response = await axios.post(`${API_URL}/api/ingest`, formData, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          );
          setUploadProgress(percentCompleted);
        }
      });

      console.log('Upload response:', response.data);

      if (!response.data || !response.data.document_id) {
        console.error('Invalid response format:', response.data);
        setError('Received invalid response from server');
        return;
      }

      setSelectedFile(null);
      setUploadProgress(0);
      
      if (isPendingChatbot) {
        // For pending chatbots, store the document info locally
        setPendingDocuments([...pendingDocuments, response.data]);
        // Notify parent component about the new document
        onDocumentsUploaded([...pendingDocuments, response.data]);
      } else {
        // For existing chatbots, refresh the documents list
        fetchDocuments(documentType);
      }
      
      setActiveTab(documentType); // Switch to the tab of the uploaded document type
    } catch (err) {
      console.error('Failed to upload document:', err);
      setError('Failed to upload document. Please try again.');
    }
  };

  const handleDeleteDocument = async (documentId) => {
    if (window.confirm('Are you sure you want to delete this document?')) {
      try {
        setError(null);
        
        if (isPendingChatbot) {
          // For pending chatbots, just remove from local state
          const updatedDocs = pendingDocuments.filter(doc => doc.id !== documentId);
          setPendingDocuments(updatedDocs);
          onDocumentsUploaded(updatedDocs);
        } else {
          // For existing chatbots, delete from server
          const token = localStorage.getItem('token');
          await axios.delete(`${API_URL}/api/documents/${documentId}`, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });
          
          // Refresh the documents list
          fetchDocuments(activeTab);
        }
      } catch (err) {
        console.error('Failed to delete document:', err);
        setError('Failed to delete document. Please try again.');
      }
    }
  };

  // Function to associate pending documents with a chatbot
  const associateDocumentsWithChatbot = async (chatbotId) => {
    if (pendingDocuments.length === 0) return;
    
    try {
      const token = localStorage.getItem('token');
      
      // Update each pending document with the chatbot ID
      const updatePromises = pendingDocuments.map(doc => 
        axios.put(`${API_URL}/api/documents/${doc.id}`, 
          { chatbot_id: chatbotId },
          { headers: { Authorization: `Bearer ${token}` }}
        )
      );
      
      await Promise.all(updatePromises);
      setPendingDocuments([]);
    } catch (err) {
      console.error('Failed to associate documents with chatbot:', err);
    }
  };

  // Function to clean up pending documents if chatbot creation is canceled
  const cleanupPendingDocuments = async () => {
    if (pendingDocuments.length === 0) return;
    
    try {
      const token = localStorage.getItem('token');
      
      // Delete each pending document
      const deletePromises = pendingDocuments.map(doc => 
        axios.delete(`${API_URL}/api/documents/${doc.id}`, 
          { headers: { Authorization: `Bearer ${token}` }}
        )
      );
      
      await Promise.all(deletePromises);
      setPendingDocuments([]);
    } catch (err) {
      console.error('Failed to clean up pending documents:', err);
    }
  };

  // Add a reset function to clear the component state
  const resetDocumentManager = () => {
    setDocuments([]);
    setPendingDocuments([]);
    setSelectedFile(null);
    setDocumentType("context");
    setUploadProgress(0);
    setActiveTab("context");
    setError(null);
    
    // Fetch documents again if we have a selected chatbot that's not pending
    if (selectedChatbot && !isPendingChatbot) {
      fetchDocuments("context");
    }
  };

  // Expose these methods to parent components
  React.useImperativeHandle(
    ref,
    () => ({
      associateDocumentsWithChatbot,
      cleanupPendingDocuments,
      pendingDocuments,
      reset: resetDocumentManager
    }),
    [pendingDocuments, selectedChatbot, isPendingChatbot]
  );

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h2 className="text-xl font-bold mb-4">Knowledge Management</h2>
      
      {/* Tab navigation */}
      <div className="border-b mb-6">
        <nav className="flex -mb-px">
          <button
            onClick={() => setActiveTab("context")}
            className={`py-2 px-4 ${
              activeTab === "context"
                ? "border-b-2 border-blue-500 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Context Documents
          </button>
          <button
            onClick={() => setActiveTab("example")}
            className={`py-2 px-4 ${
              activeTab === "example"
                ? "border-b-2 border-blue-500 text-blue-600"
                : "text-gray-500 hover:text-gray-700"
            }`}
          >
            Example Conversations
          </button>
        </nav>
      </div>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <form onSubmit={uploadDocument} className="mb-6">
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Upload Document</label>
          <input
            type="file"
            onChange={handleFileChange}
            className="w-full px-3 py-2 border rounded"
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-gray-700 mb-2">Document Type</label>
          <select
            value={documentType}
            onChange={handleDocTypeChange}
            className="w-full px-3 py-2 border rounded"
          >
            <option value="context">Context Document</option>
            <option value="example">Example Conversation</option>
          </select>
        </div>
        
        {uploadProgress > 0 && (
          <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4">
            <div 
              className="bg-blue-600 h-2.5 rounded-full" 
              style={{ width: `${uploadProgress}%` }}
            ></div>
          </div>
        )}
        
        <button
          type="submit"
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          disabled={!selectedFile}
          onClick={(e) => {
            e.preventDefault();
            uploadDocument(e);
          }}
        >
          Upload Document
        </button>
      </form>
      
      <h3 className="font-medium mb-2">
        {activeTab === "context" ? "Context Documents" : "Example Conversations"}
      </h3>
      
      {isLoading && !isPendingChatbot ? (
        <div className="text-center py-4">
          <svg className="animate-spin h-5 w-5 mx-auto text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
      ) : isPendingChatbot ? (
        pendingDocuments.length > 0 ? (
          <ul className="border rounded divide-y">
            {pendingDocuments
              .filter(doc => doc.doc_type === activeTab)
              .map(doc => (
                <li key={doc.id} className="p-3 flex justify-between items-center">
                  <div>
                    <p className="font-medium">{doc.name}</p>
                    <p className="text-xs text-gray-400">
                      Added: {new Date(doc.created_at).toLocaleDateString()}
                    </p>
                    <p className="text-xs text-gray-500">
                      (Will be associated with chatbot after creation)
                    </p>
                  </div>
                  <button 
                    onClick={() => handleDeleteDocument(doc.id)}
                    className="text-red-500 hover:text-red-700"
                  >
                    Delete
                  </button>
                </li>
              ))
            }
          </ul>
        ) : (
          <p className="text-gray-500">No {activeTab === "context" ? "context documents" : "example conversations"} uploaded yet.</p>
        )
      ) : documents.length > 0 ? (
        <ul className="border rounded divide-y">
          {documents.map(doc => (
            <li key={doc.id} className="p-3 flex justify-between items-center">
              <div>
                <p className="font-medium">{doc.name}</p>
                <p className="text-sm text-gray-500">
                  {doc.chatbot_name ? `Assigned to: ${doc.chatbot_name}` : 'Available to all chatbots'}
                </p>
                <p className="text-xs text-gray-400">
                  Added: {new Date(doc.created_at).toLocaleDateString()}
                </p>
              </div>
              <button 
                onClick={() => handleDeleteDocument(doc.id)}
                className="text-red-500 hover:text-red-700"
              >
                Delete
              </button>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-gray-500">No {activeTab === "context" ? "context documents" : "example conversations"} uploaded yet.</p>
      )}
    </div>
  );
}

export default React.forwardRef(DocumentManager);
