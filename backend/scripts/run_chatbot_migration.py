import os
import sys

# Add the parent directory to sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.abspath(os.path.join(current_dir, '..'))
sys.path.insert(0, parent_dir)

from alembic.config import Config
from alembic import command

def run_chatbot_migration():
    try:
        # Get the absolute path to alembic.ini
        alembic_ini_path = os.path.abspath(os.path.join(current_dir, '..', 'alembic.ini'))
        
        print(f"Using alembic.ini at: {alembic_ini_path}")
        
        # Create Alembic config
        alembic_cfg = Config(alembic_ini_path)
        
        # Run the specific migration
        command.upgrade(alembic_cfg, "update_chatbot_columns")
        print("Chatbot migration applied successfully!")
    except Exception as e:
        print(f"Error running chatbot migration: {e}")
        # Continue execution even if migrations fail
        print("Continuing despite migration errors...")

if __name__ == "__main__":
    print(f"Current directory: {os.getcwd()}")
    print(f"Script directory: {current_dir}")
    print(f"Parent directory: {parent_dir}")
    
    run_chatbot_migration()