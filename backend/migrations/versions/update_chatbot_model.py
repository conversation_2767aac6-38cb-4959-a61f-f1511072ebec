"""Update chatbot model

Revision ID: update_chatbot_model
Revises: create_invitation_table
Create Date: 2023-10-17 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'update_chatbot_model'
down_revision = 'create_invitation_table'  # Update this to your previous migration
branch_labels = None
depends_on = None


def upgrade() -> None:
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # Get existing columns in the chatbot table
    columns = [column['name'] for column in inspector.get_columns('chatbot')]
    
    # Add role column if it doesn't exist
    if 'role' not in columns:
        op.add_column('chatbot', sa.Column('role', sa.String()))
        print("Added 'role' column to chatbot table")
    
    # Add question column if it doesn't exist
    if 'question' not in columns:
        op.add_column('chatbot', sa.Column('question', sa.Boolean(), server_default='false'))
        print("Added 'question' column to chatbot table")
    
    # Add settings column if it doesn't exist
    if 'settings' not in columns:
        op.add_column('chatbot', sa.Column('settings', postgresql.JSONB(), server_default='{}'))
        print("Added 'settings' column to chatbot table")
    
    # Remove system_prompt column if it exists
    if 'system_prompt' in columns:
        op.drop_column('chatbot', 'system_prompt')
        print("Removed 'system_prompt' column from chatbot table")


def downgrade() -> None:
    conn = op.get_bind()
    inspector = sa.inspect(conn)
    
    # Get existing columns in the chatbot table
    columns = [column['name'] for column in inspector.get_columns('chatbot')]
    
    # Add system_prompt column back if it doesn't exist
    if 'system_prompt' not in columns:
        op.add_column('chatbot', sa.Column('system_prompt', sa.String()))
    
    # Remove new columns if they exist
    if 'settings' in columns:
        op.drop_column('chatbot', 'settings')
    
    if 'question' in columns:
        op.drop_column('chatbot', 'question')
    
    if 'role' in columns:
        op.drop_column('chatbot', 'role')
