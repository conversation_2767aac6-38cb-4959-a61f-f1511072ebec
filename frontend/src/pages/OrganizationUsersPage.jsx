import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth.jsx';
import { API_URL } from '../api/index.js';

function OrganizationUsersPage() {
  const { orgId } = useParams();
  const { auth } = useAuth();
  const [members, setMembers] = useState([]);
  const [organization, setOrganization] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [userRole, setUserRole] = useState(null);
  
  // For invitations
  const [pendingInvitations, setPendingInvitations] = useState([]);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteStatus, setInviteStatus] = useState(null);

  useEffect(() => {
    if (!auth.user) return;
    
    const fetchData = async () => {
      try {
        const token = localStorage.getItem('token');
        
        // Fetch organization details
        const orgResponse = await axios.get(`${API_URL}/api/organizations/${orgId}`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        setOrganization(orgResponse.data);
        
        // Fetch members
        const membersResponse = await axios.get(`${API_URL}/api/organizations/${orgId}/members`, {
          headers: { Authorization: `Bearer ${token}` }
        });
        setMembers(membersResponse.data);
        
        // Find current user's role
        const currentUserMembership = membersResponse.data.find(
          member => member.user_id === auth.user.id
        );
        setUserRole(currentUserMembership?.role);
        
        // Fetch pending invitations (if admin)
        if (currentUserMembership?.role === 'admin') {
          try {
            const invitationsResponse = await axios.get(`${API_URL}/api/organizations/${orgId}/invitations`, {
              headers: { Authorization: `Bearer ${token}` }
            });
            setPendingInvitations(invitationsResponse.data);
          } catch (err) {
            console.error('Error fetching invitations:', err);
            // Continue even if invitations fetch fails
          }
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load organization data');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [orgId, auth.user]);
  
  const handleInvite = async (e) => {
    e.preventDefault();
    setInviteStatus(null);
    
    try {
      const token = localStorage.getItem('token');
      await axios.post(`${API_URL}/api/invitations`, 
        {
          email: inviteEmail,
          organization_id: orgId
        },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      
      setInviteStatus({ type: 'success', message: `Invitation sent to ${inviteEmail}` });
      setInviteEmail('');
      
      // Refresh invitations list
      const invitationsResponse = await axios.get(`${API_URL}/api/organizations/${orgId}/invitations`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      setPendingInvitations(invitationsResponse.data);
      
      // Close modal after success
      setTimeout(() => {
        setShowInviteModal(false);
        setInviteStatus(null);
      }, 2000);
      
    } catch (error) {
      setInviteStatus({ 
        type: 'error', 
        message: error.response?.data?.detail || 'Failed to send invitation' 
      });
    }
  };
  
  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }
  
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
        <Link to={`/org/${orgId}/chatbots`} className="text-blue-500 hover:underline">
          Return to Chatbots
        </Link>
      </div>
    );
  }
  
  // Only admins can access this page
  if (userRole !== 'admin') {
    return (
      <div className="flex flex-col items-center justify-center h-screen">
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
          You need admin permissions to view this page.
        </div>
        <Link to={`/org/${orgId}/chatbots`} className="text-blue-500 hover:underline">
          Return to Chatbots
        </Link>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{organization.name} - Users</h1>
        <div>
          <button
            onClick={() => setShowInviteModal(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
          >
            Invite User
          </button>
          <Link
            to={`/org/${orgId}/chatbots`}
            className="ml-4 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
          >
            Back to Chatbots
          </Link>
        </div>
      </div>
      
      {/* Members List */}
      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Organization Members</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white">
            <thead>
              <tr className="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th className="py-3 px-6 text-left">Name</th>
                <th className="py-3 px-6 text-left">Email</th>
                <th className="py-3 px-6 text-left">Role</th>
              </tr>
            </thead>
            <tbody className="text-gray-600 text-sm">
              {members.map(member => (
                <tr key={member.user_id} className="border-b border-gray-200 hover:bg-gray-50">
                  <td className="py-3 px-6 text-left">{member.name}</td>
                  <td className="py-3 px-6 text-left">{member.email}</td>
                  <td className="py-3 px-6 text-left">
                    <span className={`px-2 py-1 rounded text-xs ${
                      member.role === 'admin' ? 'bg-purple-100 text-purple-800' : 'bg-blue-100 text-blue-800'
                    }`}>
                      {member.role}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Pending Invitations */}
      {pendingInvitations.length > 0 && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Pending Invitations</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white">
              <thead>
                <tr className="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                  <th className="py-3 px-6 text-left">Email</th>
                  <th className="py-3 px-6 text-left">Sent Date</th>
                  <th className="py-3 px-6 text-left">Expires</th>
                </tr>
              </thead>
              <tbody className="text-gray-600 text-sm">
                {pendingInvitations.map(invitation => (
                  <tr key={invitation.id} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="py-3 px-6 text-left">{invitation.email}</td>
                    <td className="py-3 px-6 text-left">
                      {new Date(invitation.created_at).toLocaleDateString()}
                    </td>
                    <td className="py-3 px-6 text-left">
                      {new Date(invitation.expires_at).toLocaleDateString()}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h2 className="text-xl font-bold mb-4">Invite User to {organization.name}</h2>
            
            <form onSubmit={handleInvite}>
              <div className="mb-4">
                <label className="block text-gray-700 mb-2">Email Address</label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="w-full p-2 border rounded"
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              
              {inviteStatus && (
                <div className={`p-3 mb-4 rounded ${
                  inviteStatus.type === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {inviteStatus.message}
                </div>
              )}
              
              <div className="flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowInviteModal(false)}
                  className="mr-2 px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Send Invitation
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}

export default OrganizationUsersPage;
