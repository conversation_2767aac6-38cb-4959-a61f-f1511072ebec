import asyncio
from sqlalchemy import text
from database import engine

async def migrate_chatbot_table():
    async with engine.begin() as conn:
        # Check if the columns already exist
        result = await conn.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'role'
        """))
        
        if result.rowcount == 0:
            print("Adding 'role' column to chatbot table...")
            await conn.execute(text("""
                ALTER TABLE chatbot 
                ADD COLUMN role VARCHAR
            """))
        
        result = await conn.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'chatbot' AND column_name = 'question'
        """))
        
        if result.rowcount == 0:
            print("Adding 'question' column to chatbot table...")
            await conn.execute(text("""
                ALTER TABLE chatbot 
                ADD COLUMN question BOOLEAN DEFAULT FALSE
            """))
        
        print("Migration completed successfully!")

if __name__ == "__main__":
    asyncio.run(migrate_chatbot_table())