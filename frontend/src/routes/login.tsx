// After successful login, check if the user has organizations
// and redirect accordingly
const handleLoginSuccess = async () => {
  try {
    // Fetch user's organizations
    const response = await fetch('/api/organizations', {
      headers: {
        'Authorization': `Bearer ${auth.token}`
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch organizations');
    }
    
    const organizations = await response.json();
    
    // If user has organizations, redirect to the first one
    // Otherwise, redirect to create organization page
    if (organizations && organizations.length > 0) {
      navigate(`/organizations/${organizations[0].id}`);
    } else {
      navigate('/organizations/new');
    }
  } catch (error) {
    console.error('Error checking organizations:', error);
    // Default to organization creation on error
    navigate('/organizations/new');
  }
};