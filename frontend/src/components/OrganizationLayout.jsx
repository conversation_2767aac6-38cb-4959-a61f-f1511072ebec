import React, { useState, useEffect } from 'react';
import { Outlet, useParams, Link } from 'react-router-dom';
import OrganizationSwitcher from './OrganizationSwitcher';
import { useAuth } from '../hooks/useAuth.jsx';
import axios from 'axios';
import { API_URL } from '../api/index.js';

function OrganizationLayout() {
  const { orgId } = useParams();
  const { auth } = useAuth();
  const user = auth.user;
  const [userRole, setUserRole] = useState(null);
  const [chatbots, setChatbots] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (user && orgId) {
      fetchUserRole();
      fetchChatbots();
    }
  }, [user, orgId]);
  
  const fetchUserRole = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/organizations/${orgId}/members`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      const currentUser = response.data.find(
        member => member.user_id === user.id
      );
      
      setUserRole(currentUser?.role);
    } catch (err) {
      console.error('Failed to fetch user role:', err);
    }
  };
  
  const fetchChatbots = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/chatbots`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      // Filter chatbots by organization
      const orgChatbots = response.data.filter(bot => bot.organization_id === orgId);
      setChatbots(orgChatbots);
      setLoading(false);
    } catch (err) {
      console.error('Failed to fetch chatbots:', err);
      setLoading(false);
    }
  };
  
  if (!user || loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>;
  }
  
  return (
    <div className="flex flex-col min-h-screen max-h-screen overflow-hidden">
      <header className="bg-gray-800 text-white p-4">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-xl font-bold">AI Coach App</h1>
          
          <div className="flex items-center space-x-4">
            <OrganizationSwitcher user={user} />
            
            <nav className="flex space-x-4">
              {(chatbots.length > 1 || userRole === 'admin') && (
                <Link to={`/org/${orgId}/chatbots`} className="hover:text-gray-300">
                  Chatbots
                </Link>
              )}
              {userRole === 'admin' && (
                <>
                  <Link to={`/org/${orgId}/users`} className="hover:text-gray-300">
                    Manage Users
                  </Link>
                  <Link to={`/org/${orgId}/settings`} className="hover:text-gray-300">
                    Settings
                  </Link>
                </>
              )}
            </nav>
            
            <div className="flex items-center">
              <span className="mr-2">{user.name}</span>
              <button 
                onClick={() => {
                  localStorage.removeItem('token');
                  window.location.href = '/login';
                }}
                className="text-sm bg-red-600 px-2 py-1 rounded hover:bg-red-700"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>
      
      <main className="flex-1 overflow-hidden">
        <Outlet />
      </main>
      
      <footer className="bg-gray-800 text-white p-2 text-center text-sm">
        <div className="container mx-auto">
          &copy; {new Date().getFullYear()} AI Coach App
        </div>
      </footer>
    </div>
  );
}

export default OrganizationLayout;
