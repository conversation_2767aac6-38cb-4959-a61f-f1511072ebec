from sqlalchemy import Column, String, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime
from fastapi_users.db import SQLAlchemyBaseUserTable
from fastapi_users import schemas
from pydantic import ConfigDict
from typing import Optional
from database import Base

# SQLAlchemy User Model
class User(SQLAlchemyBaseUserTable[uuid.UUID], Base):
    __tablename__ = 'user'
    
    # Explicitly define the primary key column
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    organizations = relationship("Organization", secondary="user_organization", back_populates="users")

# Pydantic models for API - using fastapi-users schemas
class UserRead(schemas.BaseUser[uuid.UUID]):
    name: str
    created_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class UserCreate(schemas.BaseUserCreate):
    name: str

class UserUpdate(schemas.BaseUserUpdate):
    name: Optional[str] = None
