import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../hooks/useAuth.jsx';
import { API_URL } from '../api/index.js';

function OrganizationSwitcher() {
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const { orgId } = useParams();
  const { auth } = useAuth();
  const user = auth.user;
  
  useEffect(() => {
    if (!user) return;
    
    // Fetch all organizations the user belongs to
    const token = localStorage.getItem('token');
    
    axios.get(`${API_URL}/api/organizations`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    })
    .then(response => {
      setOrganizations(response.data);
      setLoading(false);
    })
    .catch(error => {
      console.error('Error fetching organizations:', error);
      setLoading(false);
    });
  }, [user]);
  
  const handleOrganizationChange = (e) => {
    const newOrgId = e.target.value;
    // Navigate to the same page but with the new organization ID
    const currentPath = window.location.pathname;
    const newPath = currentPath.replace(`/org/${orgId}`, `/org/${newOrgId}`);
    navigate(newPath);
  };
  
  if (loading || organizations.length <= 1) {
    return null; // Don't show switcher if loading or only one organization
  }
  
  return (
    <div className="organization-switcher">
      <select 
        value={orgId} 
        onChange={handleOrganizationChange}
        className="border rounded p-2"
      >
        {organizations.map(org => (
          <option key={org.id} value={org.id}>
            {org.name}
          </option>
        ))}
      </select>
    </div>
  );
}

export default OrganizationSwitcher;
